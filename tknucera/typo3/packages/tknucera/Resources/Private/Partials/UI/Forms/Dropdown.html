<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="dropdownData" value="{
    'options': options
  }" />

  <f:variable name="arrowIconAdditionalAttributes" value="{'aria-hidden': 'true', 'x-bind:class': 'open ? \'rotate-180\' : \'\''}" />
  <f:variable name="searchIconAdditionalAttributes" value="{'aria-hidden': 'true'}" />

  <f:variable name="listboxButtonClass" value="relative w-full bg-white inline-flex text-darkmetalGray-250 pt-[11px] pb-[10px] items-center border border-industryGray pl-10 pr-12" />
  <f:variable name="listboxMenuClass" value="absolute w-full z-10 bg-white border-b border-l border-r border-industryGray" />
  <f:variable name="listboxOptionClass" value="hover:bg-lightPurple-25 block px-4 py-2" />
  <f:variable name="listboxOptionActiveClass" value="bg-lightPurple-25" />
  <f:variable name="listboxWrapperClass" value="relative text-darkmetalGray-250 w-48" />

  <f:if condition="{type == 'ghost'}">
    <f:then>
      <f:variable name="listboxButtonClass" value="relative w-full inline-flex pt-[11px] pb-[10px] items-center pl-10 pr-12 bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple" />
      <f:variable name="listboxMenuClass" value="absolute w-full z-10 bg-darkmetalGray-200 border-b border-l border-r border-hydroGray" />
      <f:variable name="listboxOptionClass" value="hover:bg-nuceraPurple block px-4 py-2" />
      <f:variable name="listboxOptionActiveClass" value="bg-nuceraPurple" />
    </f:then>
  </f:if>
  <f:if condition="{type == 'outline'}">
    <f:then>
      <f:variable name="listboxButtonClass" value="relative w-full inline-flex pt-[11px] pb-[10px] items-center pl-10 pr-12 bg-transparent border border-hydroGray text-hydroGray hover:bg-lightPurple-25" />
      <f:variable name="listboxMenuClass" value="absolute w-full z-10 bg-white border-b border-l border-r border-hydroGray" />
      <f:variable name="listboxOptionClass" value="hover:bg-lightPurple-25 block px-4 py-2" />
      <f:variable name="listboxOptionActiveClass" value="bg-lightPurple-25" />
    </f:then>
  </f:if>

  <div
    x-data="dropdown({dropdownData -> v:format.json.encode()})"
     @click.outside="handleClickOutside($event)"
     class="{listboxWrapperClass}">

    <span>Topline</span>
    <button
      x-ref="listbox-button"
      type="button"
      @click="toggle()"
      @keydown="handleKeydown($event)"
      :aria-expanded="open"
      aria-haspopup="listbox"
      :aria-activedescendant="open ? activedescendantId : null"
      role="combobox"
      aria-label="Select an option"
      class="{listboxButtonClass}"
    >
      <f:render
        partial="UI/Icon"
        section="Main"
        arguments="{
          name: 'google',
          class: 'absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-techGray',
          additionalAttributes: searchIconAdditionalAttributes
        }"
      />
      <span x-text="selectedItem"></span>
      <f:render
        partial="UI/Icon"
        section="Main"
        arguments="{
          name: 'chevron-down',
          class: 'absolute right-4 top-1/2 -translate-y-1/2 w-5 h-4 text-techGray transition-rotate duration-200',
          additionalAttributes: arrowIconAdditionalAttributes
        }"
      />
    </button>

    <div
      x-show="open"
      x-transition:enter="transition ease-out duration-100"
      x-transition:enter-start="transform opacity-0 scale-95"
      x-transition:enter-end="transform opacity-100 scale-100"
      x-transition:leave="transition ease-in duration-75"
      x-transition:leave-start="transform opacity-100 scale-100"
      x-transition:leave-end="transform opacity-0 scale-95"
      x-ref="listbox-menu"
      class="{listboxMenuClass}"
    >
      <div role="listbox"
        x-ref="listbox-options"
        :aria-activedescendant="activedescendantId"
        tabindex="-1"
        aria-label="Options"
        class="py-1 max-h-60 overflow-auto"
      >
        <f:for each="{options}" as="option" key="optionKey" iteration="iterator">
          <div
            id="option-{optionKey}"
            role="option"
            :aria-selected="selectedItemId === '{optionKey}'"
            @click="selectItem($event)"
            @mouseenter="handleMouseEnter({iterator.index})"
            class="{listboxOptionClass}"
            x-bind:class="{
              '{listboxOptionActiveClass}': activedescendantId === `option-{optionKey}`,
            }"
          >
           <f:format.raw>{option}</f:format.raw>
          </div>
        </f:for>
      </div>
    </div>
  </div>

</f:section>

</html>
