<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="checkboxData" value="{
    'name': name,
    'label': label,
    'value': value,
    'checked': checked,
    'disabled': disabled
  }" />


  <f:variable name="checkboxOuterClasses" value="
    absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
    z-0 w-8 h-8 rounded-full opacity-0
    bg-hydroGray group-hover:opacity-100
    group-has-[:checked]:bg-lightPurple
    pointer-events-none transition-opacity duration-200" />
  <f:variable name="checkboxCustomInputClasses" value="
    absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
    z-10 w-5 h-5 rounded-md border border-techGray flex items-center justify-center
    group-has-[:checked]:bg-nuceraPurple group-has-[:checked]:border-nuceraPurple
    peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0 peer-focus:outline-2 peer-focus:outline-accentGreen"
  />
  <f:variable name="checkboxInnerClasses" value="w-2 h-2 rounded-full bg-nuceraPurple opacity-0 group-has-[:checked]:opacity-100 transition-opacity duration-200" />

  <f:if condition="{dark}">
    <f:variable name="checkboxOuterClasses" value="
      absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-0 w-8 h-8
      rounded-full opacity-0 bg-hydroGray group-hover:opacity-100
      group-has-[:checked]:bg-lightPurple pointer-events-none transition-opacity duration-200"
    />
    <f:variable name="radioCustomInputClasses" value="
      absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
      z-10 w-5 h-5 rounded-full border border-techGray flex items-center justify-center
      group-has-[:checked]:border-nuceraPurple
      group-has-[:disabled]:opacity-50
      peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0
      peer-focus:outline-2 peer-focus:outline-accentGreen"
    />
  </f:if>

  <label class="relative cursor-pointer group inline-block w-8 h-8" x-data="{}" x-id="['checkbox']">
    <span class="sr-only">{checkboxData.label}</span>

    <input type="checkbox" name="{checkboxData.name}" value="{checkboxData.value}" :id="$id('checkbox')" class="absolute w-0 h-0 opacity-0 peer" />
    <span class="{checkboxOuterClasses}"></span>
    <span class="{checkboxCustomInputClasses}">
      <f:render partial="UI/Icon" section="Main" arguments="{name: 'checkmark', class: 'w-3 h-3 text-white hidden group-has-[:checked]:block'}" />
    </span>

  </label>

</f:section>

</html>
