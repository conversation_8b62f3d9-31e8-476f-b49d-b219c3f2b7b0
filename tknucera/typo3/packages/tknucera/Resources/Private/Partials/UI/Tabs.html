<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="tabsData" value="{
     label: {label},
     tabs: {tabs}
  }" />

<div class="w-full relative">
  <button id="tab-prev"
    class="md:hidden absolute left-0 top-1/2 -translate-y-1/2 text-white bg-darkmetalGray border-b-2 border-skyGray h-full px-4 py-2.5 z-20">
    <svg class="w-5 h-5" aria-hidden="true"><use href="assets/images/spritemap.svg#chevron-left" /></svg>
  </button>
  <button id="tab-next"
    class="md:hidden absolute right-0 top-1/2 -translate-y-1/2 text-white bg-darkmetalGray border-b-2 border-skyGray h-full px-4 py-2.5 z-20">
    <svg class="w-5 h-5" aria-hidden="true"><use href="assets/images/spritemap.svg#chevron-right" /></svg>
  </button>

  <div id="tablist-wrapper" class="overflow-hidden w-full md:overflow-visible">
    <div
      id="tablist"
      role="tablist"
      aria-label="Sample Tabs"
      class="flex flex-row transition-transform duration-300 ease-in-out md:transition-none"
    >
      <button
        role="tab"
        aria-selected="true"
        aria-controls="tabpanel-1"
        id="tab-1"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 1
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-2"
        id="tab-2"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 2
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-3"
        id="tab-3"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 3
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-4"
        id="tab-4"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 4
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-5"
        id="tab-5"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 5
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-6"
        id="tab-6"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white whitespace-nowrap min-w-0 flex-1 md:flex-initial">
        Item 6
      </button>
    </div>
  </div>
</div>

<div class="bg-darkmetalGray p-4">
  <div
    role="tabpanel"
    id="tabpanel-1"
    aria-labelledby="tab-1"
    tabindex="0"
    class="focus:outline-none text-white">
    <p>This is the content of Tab One.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-2"
    aria-labelledby="tab-2"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Two.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-3"
    aria-labelledby="tab-3"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Three.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-4"
    aria-labelledby="tab-4"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Four.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-5"
    aria-labelledby="tab-5"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Five.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-6"
    aria-labelledby="tab-6"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Six.</p>
  </div>
</div>
</f:section>
</html>
