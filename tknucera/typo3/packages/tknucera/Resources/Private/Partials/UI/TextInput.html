<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="textInputData" value="{
    'name': name,
    'label': label,
  }" />

  <f:variable name="textInputDefaultClasses" value="peer pl-12 pr-14 w-full pt-[11px] pb-[10px] px-4 focus:placeholder-transparent" />
  <f:variable name="textInputClasses" value="
    {textInputDefaultClasses} border-industryGray
    focus:border-techGray text-darkmetalGray-150 placeholder:text-urbanGray border border-industryGray"
  />
  <f:variable name="textInputSuccessClasses" value="group-has-[.success]:border-green-100 group-has-[.success]:bg-green-5" />
  <f:variable name="textInputErrorClasses" value="group-has-[.error]:border-red-warning group-has-[.error]:bg-red-5" />
  <f:variable name="textInputHelperClasses" value="
    text-extrasmall group-hover:text-darkmetalGray-250
    group-has-[.success]:text-green-100 group-has-[.error]:text-red-warning"
  />
  <f:variable name="textInputToplineClasses" value="group-hover:text-darkmetalGray-250" />
  <f:variable name="textInputIconsClass" value="text-techGray" />

  <f:if condition="{type} == 'ghost'">
    <f:variable name="textInputClasses" value="
      {textInputDefaultClasses}
      border border-transparent
      focus:border-hydroGray
      bg-darkmetalGray-200 text-hydroGray
      placeholder:text-industryGray
      hover:bg-nuceraPurple
      hover:placeholder-shown:placeholder:text-hydroGray
      focus:bg-darkmetalGray-200
      focus:hover:bg-darkmetalGray-200
      focus:hover:placeholder-transparent"
    />
    <f:variable name="textInputSuccessClasses" value="group-has-[.success]:border group-has-[.success]:border-green-100" />
    <f:variable name="textInputErrorClasses" value="group-has-[.error]:border group-has-[.error]:border-red-warning" />
    <f:variable name="textInputHelperClasses" value="text-extrasmall" />
    <f:variable name="textInputToplineClasses" value="" />
    <f:variable name="textInputIconsClass" value="text-whte" />
  </f:if>

  <f:if condition="{type} == 'outline'">
    <f:variable name="textInputClasses" value="
      {textInputDefaultClasses}
      bg-transparent border border-hydroGray-50
      focus:border-hydroGray bg-darkmetalGray-200
      text-hydroGray placeholder:text-industryGray
      hover:border-hydroGray hover:placeholder:text-hydroGray
      focus:hover:placeholder-transparent
      hover:placeholder-shown:ring hover:placeholder-shown:ring-1 hover:placeholder-shown:ring-white
      focus:ring-none focus:hover:ring-none"
    />
    <f:variable name="textInputSuccessClasses" value="group-has-[.success]:border-green-100 group-has-[.success]:ring-green-100" />
    <f:variable name="textInputErrorClasses" value="group-has-[.error]:border-red-warning group-has-[.error]:ring-red-warning" />
    <f:variable name="textInputHelperClasses" value="text-extrasmall" />
    <f:variable name="textInputToplineClasses" value="" />
    <f:variable name="textInputIconsClass" value="text-whte" />
  </f:if>

  <f:if condition="{success}">
    <f:variable name="textInputClasses" value="{textInputClasses} success" />
  </f:if>
  <f:if condition="{error}">
    <f:variable name="textInputClasses" value="{textInputClasses} error" />
  </f:if>

  <label x-data="{ value: ''}" x-id="['textInput']" class="group relative text-small inline-flex flex-col gap-1 h-full">
    <span x-id="['textInputLabel']" class="sr-only">{textInputData.label}</span>
    <span class="{textInputToplineClasses}">Topline</span>
    <input
      :id="$id('textInput')"
      type="text"
      name="{textInputData.name}"
      x-model="value"
      class="{textInputClasses} {textInputSuccessClasses} {textInputErrorClasses}"
      placeholder="Lorem Ipsum"
      :aria-labelledby="$id('textInputLabel')"

    >
    <f:render
      partial="UI/Icon"
      section="Main"
      arguments="{
        name: 'search',
        class: 'absolute left-4 top-1/2 -translate-y-1/2 size-6 {textInputIconsClass}',
        additionalAttributes: {'aria-hidden': 'true'}
      }"
    />
    <button
      type="button"
      aria-label="Clear input value"
      @click="value = ''"
      class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
      <f:render
        partial="UI/Icon"
        section="Main"
        arguments="{
          name: 'close',
          class: 'size-6 {textInputIconsClass}',
          additionalAttributes: {'aria-hidden': 'true'}
        }"
      />
    </button>
    <p class="{textInputHelperClasses}">
      Form Helper
    </p>
  </label>

</f:section>

</html>
