@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @font-face {
        font-family: 'TKType';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('../Fonts/TKTypeRegular.woff2') format('woff2'),
        url('../Fonts/TKTypeRegular.woff') format('woff'),
        url('../Fonts/TKTypeRegular.ttf') format('truetype');
    }
    @font-face {
        font-family: 'TKTypeMedium';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url('../Fonts/TKTypeMedium.woff2') format('woff2'),
        url('../Fonts/TKTypeMedium.woff') format('woff'),
        url('../Fonts/TKTypeMedium.ttf') format('truetype');
    }
    @font-face {
        font-family: 'TKTypeBold';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('../Fonts/TKTypeBold.woff2') format('woff2'),
        url('../Fonts/TKTypeBold.woff') format('woff');
    }
    @font-face {
        font-family: 'Orbitron-Bold';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('../Fonts/Orbitron-Bold.woff2') format('woff2'),
        url('../Fonts/Orbitron-Bold.woff') format('woff'),
        url('../Fonts/Orbitron-Bold.ttf') format('truetype');
    }
}

@layer components {
    body {
        @apply font-tktype text-pitchBlack;
    }
    .corporate_grid {
        @apply grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12 w-full max-w-[1280px] mx-auto;
    }

    .corporate_grid_full {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_halfLeft {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-5 lg:col-end-7;
    }

    .corporate_grid_halfRight {
        @apply col-start-1 md:col-start-5 lg:col-start-7 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_flex3Cols {
        @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
    }

    .corporate_grid_flex3Cols .corporate_grid_flexCol {
        @apply w-full md:w-1/3;
    }

    /* headlines */
    .h1 {
        @apply font-tktypeBold text-h1-mobile lg:text-h1 leading-h1-mobile lg:leading-h1;
    }

    .h2 {
        @apply font-tktypeMedium text-h2-mobile lg:text-h2 leading-h2-mobile lg:leading-h2;
    }

    .h3 {
        @apply font-tktypeMedium text-h3-mobile lg:text-h3 leading-h3-mobile lg:leading-h3;
    }

    .subtitle {
        @apply text-subtitle-mobile lg:text-subtitle leading-subtitle-mobile lg:leading-subtitle;
    }

    p a {
        @apply underline;
    }

    .h1:has(+ .h3) {
        /* if spacing should be different for headline sets */
    }

    p {
        @apply text-normal-mobile md:text-normal leading-normal-mobile md:leading-normal;
    }

    .small {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .small p {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall p {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big p {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .button {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-white bg-nuceraPurple px-6 py-4
        hover:shadow-lg group-hover:shadow-lg
        outline-offset-0
        focus:outline-accentGreen focus:outline-[2px] group-focus:outline-accentGreen group-focus:outline-[2px]
        active:outline-nuceraPurple active:bg-midPurple group-active:outline-accentGreen group-active:bg-midPurple
        disabled:bg-nuceraPurple disabled:opacity-50 disabled:pointer-events-none tracking-[0.025rem];
    }

    .button-medium {
        @apply button text-small leading-small py-3;
    }

    .button-small {
        @apply button text-small leading-small py-2 px-4;
    }

    .button-squared {
        @apply button px-4 justify-center
    }

    .button-medium-squared {
        @apply button-medium px-3 justify-center
    }

    .button-small-squared {
        @apply button-small px-2 justify-center
    }

    .button-white {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-nuceraPurple px-6 py-3.5 border outline-offset-2 border-solid border-nuceraPurple bg-oxygenWhite
        hover:shadow-lg hover:bg-lightPurple-25
        active:bg-lightPurple-50
        focus:outline-accentGreen focus:outline-[2px]
        disabled:border-nuceraPurple
        disabled:text-nuceraPurple disabled:pointer-events-none  disabled:opacity-50 tracking-[0.025rem];
    }

    .button-medium-white {
        @apply button-white text-small leading-small py-2.5;
    }

    .button-small-white {
        @apply button-white text-small leading-small py-1.5 px-4;
    }

    .button-white-squared {
        @apply button-white px-4 justify-center;
    }

    .button-medium-white-squared {
        @apply button-medium-white px-3 justify-center;
    }

    .button-small-white-squared {
        @apply button-small-white px-2 justify-center;
    }

    .button-dark {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-oxygenWhite px-6 py-3.5 border outline-offset-2 border-solid border-oxygenWhite
        hover:shadow-lg hover:bg-whiteOpacity12
        active:bg-whiteOpacity20
        focus:outline-accentGreen focus:outline-[2px]
        disabled:pointer-events-none disabled:opacity-50 tracking-[2.5%];
    }

    .button-medium-dark {
        @apply button-dark text-small leading-small py-2.5;
    }

    .button-small-dark {
        @apply button-dark text-small leading-small py-1.5 px-4;
    }

    .button-dark-squared {
        @apply button-dark px-4 justify-center;
    }

    .button-medium-dark-squared {
        @apply button-medium-dark px-3 justify-center;
    }

    .button-small-dark-squared {
        @apply button-small-dark px-2 justify-center;
    }

    /* only for styleguide */
    .layoutPlaceholder {
        @apply bg-darkmetalGray w-full text-white p-4;
    }

    .colorList {
        @apply flex flex-row gap-6 mb-12 flex-wrap;
    }
    .colorList > div {
        @apply h-24 w-24 rounded-full shadow-lg flex items-center justify-center;
    }

    .highlightHeadline {
        @apply leading-highlighted;
    }

    .highlightHeadline span {
        @apply inline-block px-2 pt-1 lg:pt-2 -mr-4 lg:px-3.5 lg:-mr-7 mb-3;
    }

    h2.highlightHeadline span {
        @apply pt-1 px-1.5 -mr-3 lg:px-2.5 lg:-mr-5 mb-3;
    }

    h3.highlightHeadline span {
        @apply pt-1 px-1 -mr-2 lg:px-2 lg:-mr-4 mb-2;
    }

    .highlightPositive span {
        @apply bg-nuceraPurple text-white;
    }

    .highlightNegative span {
        @apply bg-white text-nuceraPurple;
    }

    .container-dark {
        @apply text-oxygenWhite bg-darkmetalGray-150 py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
    }

    .container-dark-noMarginTop {
        @apply container-dark pt-0;
    }

    .container-white {
        @apply bg-oxygenWhite text-pitchBlack py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
    }

    .container-white-noMarginTop {
        @apply container-white pt-0;
    }

    .container-bgImageLight {
        @apply bg-oxygenWhite text-pitchBlack px-0;
    }

    .container-bgImageDark {
        @apply text-oxygenWhite bg-darkmetalGray-150 px-0;
    }

    .input {
        @apply w-full pt-[11px] pb-[10px] px-4 focus:placeholder-transparent;
    }

    .input-error,
    .input-success {
        @apply valid:border-2 invalid:border-2
    }

    .textInput-standard {
        @apply input border-industryGray focus:border-techGray text-darkmetalGray-150 placeholder:text-urbanGray border border-industryGray;
    }

    .textInput-standard-success {
        @apply textInput-standard input-success valid:border-green-100 valid:bg-green-5 ;
    }

    .textInput-standard-error {
        @apply textInput-standard input-error invalid:border-red-warning invalid:bg-red-5;
    }

    .textInput-ghost {
        @apply input border border-transparent
        focus:border-hydroGray
        bg-darkmetalGray-200 text-hydroGray
        placeholder:text-industryGray
        hover:bg-nuceraPurple
        hover:placeholder-shown:placeholder:text-hydroGray
        focus:bg-darkmetalGray-200
        focus:hover:bg-darkmetalGray-200
        focus:hover:placeholder-transparent;
    }

    .textInput-ghost-success {
        @apply textInput-ghost input-success valid:border-green-100;
    }

    .textInput-ghost-error {
        @apply textInput-ghost input-error invalid:border-red-warning;
    }

    .textInput-outline {
        @apply input bg-transparent border border-hydroGray-50
        focus:border-hydroGray bg-darkmetalGray-200
        text-hydroGray placeholder:text-industryGray
        hover:border-hydroGray hover:placeholder:text-hydroGray
        focus:hover:placeholder-transparent
        hover:placeholder-shown:outline hover:placeholder-shown:outline-1 hover:placeholder-shown:outline-white
        focus:outline-none focus:hover:outline-none;
    }

    .textInput-outline-success {
        @apply textInput-outline input-success valid:border-green-100;
    }

    .textInput-outline-error {
        @apply textInput-outline input-error invalid:border-red-warning;
    }

}

@layer utilities {
    .font-smaller {
        font-size: 75%;
    }
    .font-larger {
        font-size: 120%;
    }
}

.forced-screen-width {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.forced-screen-width-unset {
    width: unset;
    position: unset;
    left: unset;
    right: unset;
    margin-left: unset;
    margin-right: unset;
}

.vf-container {
    position: relative;
    top: 0;
    left: 0;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.vf-container img {
    position: absolute;
    left: 0;
    top: 0;
    margin: 0;
    display: block;
    /* fill and maintain aspect ratio */
    width: auto; height: auto;
    min-width: 100%; min-height: 100%;
    max-height: none; max-width: none;
}
