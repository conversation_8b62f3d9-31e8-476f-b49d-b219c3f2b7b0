<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:focuspoint="http://typo3.org/ns/HDNET/Focuspoint/ViewHelpers"
      xmlns:m="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      xmlns:ybpn="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default" />
<f:section name="Configuration">
  <flux:form id="styleguide"
             label="tknucera: Styleguide Layout"
             description="Styleguide Layout für die Darstellung von Design-Elementen und Komponenten">
    <flux:grid>
      <flux:grid.row>
        <flux:grid.column colPos="0" name="content" />
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Main">
  <!--TYPO3SEARCH_begin-->
  <div class="styleguide-container bg-softWhite p-8">
    <div class="corporate_grid mb-8">
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Dropdown normal</div>
        <div class="flex flex-row flex-wrap p-4">
          <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'} }" />
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Dropdown ghost</div>
        <div class="flex flex-row flex-wrap p-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
          <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'ghost'}" />
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Dropdown outline</div>
        <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
            <div class=" p-4 rounded-lg flex flex-row flex-wrap min-h-[270px]" style="width:100%;background: rgba(255, 255, 255, .2);
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);">
              <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'outline'}" />
            </div>
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
         <div class="h3 my-4">Textarea normal</div>
          <div class="flex flex-row flex-wrap p-4 gap-4">
            <f:render partial="UI/Textarea" section="Main" arguments="{_all}" />
            <f:render partial="UI/Textarea" section="Main" arguments="{success: 1}" />
            <f:render partial="UI/Textarea" section="Main" arguments="{error: 1}" />
          </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Textarea ghost</div>
        <div class="flex flex-row flex-wrap p-4 gap-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
          <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost'}" />
          <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost', success: 1}" />
          <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost', error: 1}" />
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Textarea outline</div>
        <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
            <div class="p-4 rounded-lg flex flex-row flex-wrap min-h-[270px] gap-4" style="width:100%;background: rgba(255, 255, 255, .2);
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);">
              <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline'}" />
              <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline', success: 1}" />
              <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline', error: 1}" />
            </div>
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Text input normal</div>
        <div class="flex flex-row flex-wrap p-4 gap-4">
          <f:render partial="UI/Textinput" section="Main" arguments="{_all}" />
          <f:render partial="UI/Textinput" section="Main" arguments="{success: 1}" />
          <f:render partial="UI/Textinput" section="Main" arguments="{error: 1}" />
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Text input ghost</div>
        <div class="flex flex-row flex-wrap p-4 gap-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
          <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost'}" />
          <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost', success: 1}" />
          <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost', error: 1}" />
        </div>
      </div>
      <div class="corporate_grid_full mb-12">
        <div class="h3 my-4">Text input outline</div>
        <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
            <div class=" p-4 rounded-lg flex flex-row flex-wrap min-h-[270px] gap-4" style="width:100%;background: rgba(255, 255, 255, .2);
            -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);">
              <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline'}" />
              <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline', success: 1}" />
              <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline', error: 1}" />
            </div>
        </div>
      </div>
    </div>

  </div>
  <!--TYPO3SEARCH_end-->
</f:section>
</html>
