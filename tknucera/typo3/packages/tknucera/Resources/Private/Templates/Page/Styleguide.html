<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:focuspoint="http://typo3.org/ns/HDNET/Focuspoint/ViewHelpers"
      xmlns:m="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      xmlns:ybpn="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default" />
<f:section name="Configuration">
  <flux:form id="styleguide"
             label="tknucera: Styleguide Layout"
             description="Styleguide Layout für die Darstellung von Design-Elementen und Komponenten">
    <flux:grid>
      <flux:grid.row>
        <flux:grid.column colPos="0" name="content" />
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Main">
  <!--TYPO3SEARCH_begin-->
  <div class="styleguide-container bg-softWhite p-8">
    <div class="corporate_grid mb-8">
      <div class="corporate_grid_full">
        <f:render partial="UI/Forms/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'} }" />
      </div>
      <div class="corporate_grid_full">
        <div class="h3 my-16">Dropdown ghost</div>
          <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 bg-darkmetalGray-150 text-skyGray h-[300px]">
            <f:render partial="UI/Forms/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'ghost'}" />
          </div>
      </div>
      <div class="corporate_grid_full">
        <div class="h3 my-16">Dropdown outline</div>
          <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 h-[400px]">
              <div class=" p-4 rounded-lg flex flex-row gap-8 flex-wrap h-full" style="width:100%;background: rgba(255, 255, 255, .2);
              -webkit-backdrop-filter: blur(4px);
              backdrop-filter: blur(4px);">
                <f:render partial="UI/Forms/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'outline'}" />
              </div>
          </div>
      </div>
    </div>
  </div>
  <!--TYPO3SEARCH_end-->
</f:section>
</html>
